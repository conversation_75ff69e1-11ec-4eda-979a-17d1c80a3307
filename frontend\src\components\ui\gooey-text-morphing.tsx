"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface GooeyTextProps {
  texts: string[];
  morphTime?: number;
  cooldownTime?: number;
  className?: string;
  textClassName?: string;
}

export function GooeyText({
  texts,
  morphTime = 1,
  cooldownTime = 0.25,
  className,
  textClassName
}: GooeyTextProps) {
  const text1Ref = React.useRef<HTMLSpanElement>(null);
  const text2Ref = React.useRef<HTMLSpanElement>(null);
  const animationRef = React.useRef<number>();

  React.useEffect(() => {
    if (texts.length === 0) return;

    let currentTextIndex = 0;
    let startTime = Date.now();
    let phase: 'display' | 'morphing' = 'display';
    let isAnimating = true;

    // Initialize with first text
    if (text1Ref.current && text2Ref.current) {
      text1Ref.current.textContent = texts[0];
      text2Ref.current.textContent = texts[1 % texts.length];

      // Show first text clearly
      text1Ref.current.style.filter = "";
      text1Ref.current.style.opacity = "100%";
      text2Ref.current.style.filter = "";
      text2Ref.current.style.opacity = "0%";
    }

    const easeInOutCubic = (t: number): number => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    const animate = () => {
      if (!isAnimating) return;

      const now = Date.now();
      const elapsed = (now - startTime) / 1000;

      if (phase === 'display') {
        // Display current text for cooldownTime
        if (elapsed >= cooldownTime) {
          phase = 'morphing';
          startTime = now;
        }
      } else if (phase === 'morphing') {
        // Morph to next text
        const progress = Math.min(elapsed / morphTime, 1);
        const easedProgress = easeInOutCubic(progress);

        if (text1Ref.current && text2Ref.current) {
          // Current text fades out with blur
          const currentOpacity = 1 - easedProgress;
          const currentBlur = easedProgress * 8;

          // Next text fades in with blur reduction
          const nextOpacity = easedProgress;
          const nextBlur = (1 - easedProgress) * 8;

          text1Ref.current.style.opacity = `${currentOpacity * 100}%`;
          text1Ref.current.style.filter = currentBlur > 0.1 ? `blur(${currentBlur}px)` : "";

          text2Ref.current.style.opacity = `${nextOpacity * 100}%`;
          text2Ref.current.style.filter = nextBlur > 0.1 ? `blur(${nextBlur}px)` : "";
        }

        if (progress >= 1) {
          // Morphing complete - advance to next text
          currentTextIndex = (currentTextIndex + 1) % texts.length;
          const nextTextIndex = (currentTextIndex + 1) % texts.length;

          if (text1Ref.current && text2Ref.current) {
            // Swap: what was text2 becomes text1, load new text2
            text1Ref.current.textContent = texts[currentTextIndex];
            text2Ref.current.textContent = texts[nextTextIndex];

            // Reset to display state
            text1Ref.current.style.filter = "";
            text1Ref.current.style.opacity = "100%";
            text2Ref.current.style.filter = "";
            text2Ref.current.style.opacity = "0%";
          }

          phase = 'display';
          startTime = now;
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      isAnimating = false;
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [texts, morphTime, cooldownTime]);

  // Generate unique filter ID to avoid conflicts
  const filterId = React.useMemo(() => `gooey-threshold-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={cn("relative", className)}>
      <svg className="absolute h-0 w-0" aria-hidden="true" focusable="false">
        <defs>
          <filter id={filterId}>
            <feColorMatrix
              in="SourceGraphic"
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 255 -140"
            />
          </filter>
        </defs>
      </svg>

      <div
        className="flex items-center justify-center"
        style={{ filter: `url(#${filterId})` }}
      >
        <span
          ref={text1Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
        <span
          ref={text2Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
      </div>
    </div>
  );
}
