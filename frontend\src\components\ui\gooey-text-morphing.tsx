"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface GooeyTextProps {
  texts: string[];
  morphTime?: number;
  cooldownTime?: number;
  className?: string;
  textClassName?: string;
}

export function GooeyText({
  texts,
  morphTime = 1,
  cooldownTime = 0.25,
  className,
  textClassName
}: GooeyTextProps) {
  const text1Ref = React.useRef<HTMLSpanElement>(null);
  const text2Ref = React.useRef<HTMLSpanElement>(null);
  const animationRef = React.useRef<number>();

  React.useEffect(() => {
    if (texts.length === 0) return;

    let textIndex = 0;
    let startTime = performance.now();
    let isDisplaying = true; // true = showing text, false = morphing
    let animationId: number;

    // Set initial text
    if (text1Ref.current && text2Ref.current) {
      text1Ref.current.textContent = texts[0];
      text2Ref.current.textContent = texts[1 % texts.length];

      // Initial state: show first text
      text1Ref.current.style.opacity = "1";
      text1Ref.current.style.filter = "";
      text2Ref.current.style.opacity = "0";
      text2Ref.current.style.filter = "";
    }

    const animate = (currentTime: number) => {
      const elapsed = (currentTime - startTime) / 1000;

      if (isDisplaying) {
        // Display phase - show current text
        if (elapsed >= cooldownTime) {
          // Switch to morphing phase
          isDisplaying = false;
          startTime = currentTime;
        }
      } else {
        // Morphing phase - transition to next text
        const progress = Math.min(elapsed / morphTime, 1);

        // Smooth easing
        const eased = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        if (text1Ref.current && text2Ref.current) {
          // Fade out current text with blur
          text1Ref.current.style.opacity = String(1 - eased);
          text1Ref.current.style.filter = eased > 0.1 ? `blur(${eased * 8}px)` : "";

          // Fade in next text with blur reduction
          text2Ref.current.style.opacity = String(eased);
          text2Ref.current.style.filter = (1 - eased) > 0.1 ? `blur(${(1 - eased) * 8}px)` : "";
        }

        if (progress >= 1) {
          // Morphing complete - move to next text
          textIndex = (textIndex + 1) % texts.length;
          const nextIndex = (textIndex + 1) % texts.length;

          if (text1Ref.current && text2Ref.current) {
            // Swap texts: next becomes current
            text1Ref.current.textContent = texts[textIndex];
            text2Ref.current.textContent = texts[nextIndex];

            // Reset to display state
            text1Ref.current.style.opacity = "1";
            text1Ref.current.style.filter = "";
            text2Ref.current.style.opacity = "0";
            text2Ref.current.style.filter = "";
          }

          isDisplaying = true;
          startTime = currentTime;
        }
      }

      animationId = requestAnimationFrame(animate);
    };

    // Start animation
    animationId = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, [texts, morphTime, cooldownTime]);

  // Generate unique filter ID to avoid conflicts
  const filterId = React.useMemo(() => `gooey-threshold-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={cn("relative", className)}>
      <svg className="absolute h-0 w-0" aria-hidden="true" focusable="false">
        <defs>
          <filter id={filterId}>
            <feColorMatrix
              in="SourceGraphic"
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 255 -140"
            />
          </filter>
        </defs>
      </svg>

      <div
        className="flex items-center justify-center"
        style={{ filter: `url(#${filterId})` }}
      >
        <span
          ref={text1Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
        <span
          ref={text2Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
      </div>
    </div>
  );
}
