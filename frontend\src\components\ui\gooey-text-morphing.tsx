"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface GooeyTextProps {
  texts: string[];
  morphTime?: number;
  cooldownTime?: number;
  className?: string;
  textClassName?: string;
}

export function GooeyText({
  texts,
  morphTime = 1,
  cooldownTime = 0.25,
  className,
  textClassName
}: GooeyTextProps) {
  const text1Ref = React.useRef<HTMLSpanElement>(null);
  const text2Ref = React.useRef<HTMLSpanElement>(null);
  const animationRef = React.useRef<number>();
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [nextIndex, setNextIndex] = React.useState(1);

  React.useEffect(() => {
    if (texts.length === 0) return;

    let startTime = Date.now();
    let phase: 'cooldown' | 'morphing' = 'cooldown';
    let morphProgress = 0;

    // Initialize text content
    if (text1Ref.current && text2Ref.current) {
      text1Ref.current.textContent = texts[currentIndex];
      text2Ref.current.textContent = texts[nextIndex % texts.length];

      // Initial state - show first text
      text1Ref.current.style.filter = "";
      text1Ref.current.style.opacity = "100%";
      text2Ref.current.style.filter = "";
      text2Ref.current.style.opacity = "0%";
    }

    const animate = () => {
      const now = Date.now();
      const elapsed = (now - startTime) / 1000;

      if (phase === 'cooldown') {
        if (elapsed >= cooldownTime) {
          // Switch to morphing phase
          phase = 'morphing';
          startTime = now;
          morphProgress = 0;
        }
      } else if (phase === 'morphing') {
        morphProgress = Math.min(elapsed / morphTime, 1);

        if (text1Ref.current && text2Ref.current) {
          // Smooth morphing transition
          const easeProgress = easeInOutCubic(morphProgress);

          // Text2 (next) fades in with blur reduction
          const text2Opacity = easeProgress;
          const text2Blur = Math.max(0, (1 - easeProgress) * 8);

          // Text1 (current) fades out with blur increase
          const text1Opacity = 1 - easeProgress;
          const text1Blur = Math.max(0, easeProgress * 8);

          text2Ref.current.style.opacity = `${text2Opacity * 100}%`;
          text2Ref.current.style.filter = text2Blur > 0 ? `blur(${text2Blur}px)` : "";

          text1Ref.current.style.opacity = `${text1Opacity * 100}%`;
          text1Ref.current.style.filter = text1Blur > 0 ? `blur(${text1Blur}px)` : "";
        }

        if (morphProgress >= 1) {
          // Morphing complete, switch texts and reset
          const newCurrentIndex = nextIndex % texts.length;
          const newNextIndex = (nextIndex + 1) % texts.length;

          setCurrentIndex(newCurrentIndex);
          setNextIndex(newNextIndex);

          if (text1Ref.current && text2Ref.current) {
            // Swap the text content
            text1Ref.current.textContent = texts[newCurrentIndex];
            text2Ref.current.textContent = texts[newNextIndex];

            // Reset to initial state
            text1Ref.current.style.filter = "";
            text1Ref.current.style.opacity = "100%";
            text2Ref.current.style.filter = "";
            text2Ref.current.style.opacity = "0%";
          }

          phase = 'cooldown';
          startTime = now;
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    // Easing function for smooth transitions
    const easeInOutCubic = (t: number): number => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [texts, morphTime, cooldownTime, currentIndex, nextIndex]);

  // Generate unique filter ID to avoid conflicts
  const filterId = React.useMemo(() => `gooey-threshold-${Math.random().toString(36).substr(2, 9)}`, []);

  return (
    <div className={cn("relative", className)}>
      <svg className="absolute h-0 w-0" aria-hidden="true" focusable="false">
        <defs>
          <filter id={filterId}>
            <feColorMatrix
              in="SourceGraphic"
              type="matrix"
              values="1 0 0 0 0
                      0 1 0 0 0
                      0 0 1 0 0
                      0 0 0 255 -140"
            />
          </filter>
        </defs>
      </svg>

      <div
        className="flex items-center justify-center"
        style={{ filter: `url(#${filterId})` }}
      >
        <span
          ref={text1Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
        <span
          ref={text2Ref}
          className={cn(
            "absolute inline-block select-none text-center text-6xl md:text-[60pt]",
            "text-white font-bold",
            textClassName
          )}
        />
      </div>
    </div>
  );
}
