import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { HoveredLink, Menu, MenuItem } from './ui/navbar-menu';
import { cn } from '@/lib/utils';

const ModernNavbar = () => {
  const [active, setActive] = useState<string | null>(null);
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [hasBeenResized, setHasBeenResized] = useState(false);

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const isScrolled = window.scrollY > 100;
          setScrolled(isScrolled);

          // Once the navbar has been resized (scrolled), keep it compact
          if (isScrolled && !hasBeenResized) {
            setHasBeenResized(true);
          }

          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <motion.div
      className="navbar fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-out"
      style={{ backgroundColor: 'transparent' }}
      animate={{
        // Only apply padding on desktop screens
        paddingTop: !isMobile && scrolled ? "0.5rem" : "0rem",
        paddingLeft: !isMobile && scrolled ? "1rem" : "0rem",
        paddingRight: !isMobile && scrolled ? "1rem" : "0rem"
      }}
      transition={{ duration: 1.2, ease: [0.16, 1, 0.3, 1] }}
    >
      <div className="flex justify-center">
        <motion.div
          initial={{
            width: "100%",
            maxWidth: "100%",
          }}
          animate={{
            // Mobile: Always full width
            // Desktop: Full width initially, then compact once resized
            width: isMobile ? "100%" : (hasBeenResized ? "auto" : "100%"),
            maxWidth: isMobile ? "100%" : (hasBeenResized ? "56rem" : "100%"),
          }}
          transition={{
            duration: 1.2,
            ease: [0.16, 1, 0.3, 1],
            width: { duration: 1.2, ease: [0.16, 1, 0.3, 1] },
            maxWidth: { duration: 1.2, ease: [0.16, 1, 0.3, 1] }
          }}
          className={cn(
            "transition-colors duration-[1200ms] ease-[cubic-bezier(0.16,1,0.3,1)]",
            // Mobile: Always full width, no reshaping
            "w-full",
            // Desktop: Conditional styling with reshaping (rounded corners, margins, background)
            (hasBeenResized && scrolled)
              ? "lg:bg-black/90 lg:backdrop-blur-xl lg:shadow-2xl lg:rounded-full lg:border lg:border-white/20 lg:mx-0"
              : "lg:bg-transparent lg:backdrop-blur-none lg:shadow-none lg:rounded-none lg:border-none lg:mx-0",
            // Mobile: Background when menu is open, transparent when closed - NO reshaping
            mobileMenuOpen
              ? "bg-black/90 backdrop-blur-xl border-b border-white/10"
              : "bg-transparent backdrop-blur-none border-none"
          )}
          style={{
            backdropFilter: ((hasBeenResized && scrolled) && !isMobile) || (isMobile && mobileMenuOpen) ? 'blur(20px) saturate(180%)' : 'none',
          }}
        >
          <div className="flex items-center w-full justify-between px-4 py-3 lg:hidden">
            {/* Mobile Logo */}
            <Link to="/" className="flex items-center">
              <img
                src="https://sdwgyjjcxdhdlcuvjadq.supabase.co/storage/v1/object/public/invoices//delta_zero_vertical_logo-removebg-preview.png"
                alt="Delta Xero Creations Logo"
                className="h-16 w-auto object-contain"
              />
            </Link>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2 text-white hover:text-primary-400 transition-colors"
              aria-label="Toggle mobile menu"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center">
                <span className={`bg-current block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${mobileMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'}`}></span>
                <span className={`bg-current block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5 ${mobileMenuOpen ? 'opacity-0' : 'opacity-100'}`}></span>
                <span className={`bg-current block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${mobileMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'}`}></span>
              </div>
            </button>
          </div>

          <motion.div
            className="hidden lg:flex items-center w-full"
            initial={{
              justifyContent: "center",
              paddingLeft: "2rem",
              paddingRight: "2rem",
              paddingTop: "1.5rem",
              paddingBottom: "1.5rem",
            }}
            animate={{
              justifyContent: scrolled ? "space-between" : "center",
              paddingLeft: scrolled ? "2rem" : "2rem",
              paddingRight: scrolled ? "2rem" : "2rem",
              paddingTop: scrolled ? "0.75rem" : "1.5rem",
              paddingBottom: scrolled ? "0.75rem" : "1.5rem",
            }}
            transition={{
              duration: 0.8,
              ease: [0.23, 1, 0.32, 1],
              justifyContent: { duration: 0.8, ease: [0.23, 1, 0.32, 1] },
              paddingLeft: { duration: 0.8, ease: [0.23, 1, 0.32, 1] },
              paddingRight: { duration: 0.8, ease: [0.23, 1, 0.32, 1] },
              paddingTop: { duration: 0.8, ease: [0.23, 1, 0.32, 1] },
              paddingBottom: { duration: 0.8, ease: [0.23, 1, 0.32, 1] }
            }}
          >
            {/* Logo */}
            <Link to="/" className="flex items-center">
              <motion.img
                src="https://sdwgyjjcxdhdlcuvjadq.supabase.co/storage/v1/object/public/invoices//delta_zero_vertical_logo-removebg-preview.png"
                alt="Delta Xero Creations Logo"
                whileHover={{ scale: 1.05 }}
                initial={{
                  height: "6rem",
                  width: "auto",
                  opacity: 1,
                }}
                animate={{
                  height: scrolled ? "5rem" : "6rem",
                  width: "auto",
                  opacity: scrolled ? 1 : 1,
                }}
                transition={{
                  duration: 1.2,
                  ease: [0.16, 1, 0.3, 1],
                  height: { duration: 1.2, ease: [0.16, 1, 0.3, 1] }
                }}
                className="object-contain"
              />
            </Link>

            {/* Desktop Menu */}
            <motion.div
              className="hidden lg:flex flex-1 justify-center"
              animate={{
                opacity: scrolled ? 0.95 : 1,
                scale: scrolled ? 0.98 : 1,
              }}
              transition={{
                duration: 1.2,
                ease: [0.16, 1, 0.3, 1],
                opacity: { duration: 1.2, ease: [0.16, 1, 0.3, 1] },
                scale: { duration: 1.2, ease: [0.16, 1, 0.3, 1] }
              }}
            >
              <Menu setActive={setActive}>
                {/* Simple Services Link - No Hover Popup */}
                <Link
                  to="/services"
                  onMouseEnter={() => setActive(null)} // Close any open dropdowns when hovering over Services
                  className="text-white hover:text-primary-400 transition-colors duration-300 px-4 py-2 rounded-lg font-medium"
                >
                  Services
                </Link>

                {/* Solutions Menu */}
                <MenuItem setActive={setActive} active={active} item="Solutions">
                  <div className="flex w-[700px] p-6 gap-6">
                    {/* Development Solutions */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-4 pb-2 border-b border-gray-700/50">
                        <div className="w-3 h-3 bg-gradient-to-r from-primary-400 to-primary-500 rounded-full mr-3"></div>
                        <h3 className="text-white font-semibold text-sm tracking-wide">Development Solutions</h3>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <HoveredLink to="/web-development" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-blue-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500/20 to-blue-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-blue-400 text-sm">🌐</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-blue-300 transition-colors">Web Development</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Custom websites & web applications</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/mobile-development" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-green-500/10 hover:to-green-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-green-400 text-sm">📱</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-green-300 transition-colors">Mobile Development</div>
                              <div className="text-gray-400 text-xs leading-relaxed">iOS & Android applications</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/api-development" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-cyan-500/10 hover:to-cyan-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-cyan-500/20 to-cyan-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-cyan-400 text-sm">⚡</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-cyan-300 transition-colors">API Development</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Backend services & integrations</div>
                            </div>
                          </div>
                        </HoveredLink>
                      </div>
                    </div>

                    {/* Vertical Divider */}
                    <div className="w-px bg-gradient-to-b from-transparent via-gray-600/50 to-transparent"></div>

                    {/* Design & Strategy */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-4 pb-2 border-b border-gray-700/50">
                        <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full mr-3"></div>
                        <h3 className="text-white font-semibold text-sm tracking-wide">Design & Strategy</h3>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <HoveredLink to="/ui-ux-design" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-purple-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-purple-500/20 to-purple-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-purple-400 text-sm">🎨</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-purple-300 transition-colors">UI/UX Design</div>
                              <div className="text-gray-400 text-xs leading-relaxed">User-centered design solutions</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/digital-transformation" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-orange-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-orange-500/20 to-orange-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-orange-400 text-sm">🚀</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-orange-300 transition-colors">Digital Transformation</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Modernize your business</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/brand-strategy" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-pink-500/10 hover:to-pink-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-pink-500/20 to-pink-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-pink-400 text-sm">📊</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-pink-300 transition-colors">Brand Strategy</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Brand identity & positioning</div>
                            </div>
                          </div>
                        </HoveredLink>
                      </div>
                    </div>
                  </div>
                </MenuItem>

                {/* Industries Menu */}
                <MenuItem setActive={setActive} active={active} item="Industries">
                  <div className="flex w-[650px] p-6 gap-6">
                    {/* Technology & Business */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-4 pb-2 border-b border-gray-700/50">
                        <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-blue-500 rounded-full mr-3"></div>
                        <h3 className="text-white font-semibold text-sm tracking-wide">Technology & Business</h3>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <HoveredLink to="/ecommerce" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-emerald-500/10 hover:to-emerald-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-emerald-500/20 to-emerald-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-emerald-400 text-sm">🛒</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-emerald-300 transition-colors">E-commerce</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Online retail solutions</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/saas" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-blue-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500/20 to-blue-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-blue-400 text-sm">💼</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-blue-300 transition-colors">SaaS Platforms</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Software as a Service</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/education" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-violet-500/10 hover:to-violet-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-violet-500/20 to-violet-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-violet-400 text-sm">🎓</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-violet-300 transition-colors">Education</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Learning management systems</div>
                            </div>
                          </div>
                        </HoveredLink>
                      </div>
                    </div>

                    {/* Vertical Divider */}
                    <div className="w-px bg-gradient-to-b from-transparent via-gray-600/50 to-transparent"></div>

                    {/* Healthcare & Finance */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-4 pb-2 border-b border-gray-700/50">
                        <div className="w-3 h-3 bg-gradient-to-r from-red-400 to-yellow-500 rounded-full mr-3"></div>
                        <h3 className="text-white font-semibold text-sm tracking-wide">Healthcare & Finance</h3>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <HoveredLink to="/healthcare" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-red-500/10 hover:to-red-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-red-500/20 to-red-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-red-400 text-sm">🏥</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-red-300 transition-colors">Healthcare</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Medical & wellness platforms</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/fintech" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-yellow-500/10 hover:to-yellow-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-yellow-500/20 to-yellow-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-yellow-400 text-sm">💰</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-yellow-300 transition-colors">Fintech</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Financial technology solutions</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/banking" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-indigo-500/10 hover:to-indigo-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-500/20 to-indigo-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-indigo-400 text-sm">🏦</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-indigo-300 transition-colors">Banking</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Digital banking solutions</div>
                            </div>
                          </div>
                        </HoveredLink>
                      </div>
                    </div>
                  </div>
                </MenuItem>

                {/* Company Menu */}
                <MenuItem setActive={setActive} active={active} item="Company">
                  <div className="flex w-[550px] p-6 gap-6">
                    {/* About & Culture */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-4 pb-2 border-b border-gray-700/50">
                        <div className="w-3 h-3 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full mr-3"></div>
                        <h3 className="text-white font-semibold text-sm tracking-wide">About & Culture</h3>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <HoveredLink to="/about" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-indigo-500/10 hover:to-indigo-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-500/20 to-indigo-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-indigo-400 text-sm">🏢</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-indigo-300 transition-colors">About Us</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Our story and mission</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/jobs" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-green-500/10 hover:to-green-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-green-500/20 to-green-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-green-400 text-sm">👥</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-green-300 transition-colors">Careers</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Join our growing team</div>
                            </div>
                          </div>
                        </HoveredLink>
                      </div>
                    </div>

                    {/* Vertical Divider */}
                    <div className="w-px bg-gradient-to-b from-transparent via-gray-600/50 to-transparent"></div>

                    {/* Resources & Contact */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-4 pb-2 border-b border-gray-700/50">
                        <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-orange-500 rounded-full mr-3"></div>
                        <h3 className="text-white font-semibold text-sm tracking-wide">Resources & Contact</h3>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <HoveredLink to="/blog" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-purple-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-purple-500/20 to-purple-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-purple-400 text-sm">📝</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-purple-300 transition-colors">Blog</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Latest insights & updates</div>
                            </div>
                          </div>
                        </HoveredLink>
                        <HoveredLink to="/get-quote" setActive={setActive} className="group">
                          <div className="flex items-center space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-orange-600/10 transition-all duration-300">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-orange-500/20 to-orange-600/30 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <span className="text-orange-400 text-sm">💬</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-white font-medium text-sm group-hover:text-orange-300 transition-colors">Get Quote</div>
                              <div className="text-gray-400 text-xs leading-relaxed">Start your project today</div>
                            </div>
                          </div>
                        </HoveredLink>
                      </div>
                    </div>
                  </div>
                </MenuItem>
              </Menu>
            </motion.div>



            {/* CTA Button - Desktop */}
            <div className="hidden lg:block">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              animate={{
                scale: scrolled ? 0.95 : 1,
              }}
              transition={{
                duration: 1.2,
                ease: [0.16, 1, 0.3, 1],
                scale: { duration: 1.2, ease: [0.16, 1, 0.3, 1] }
              }}
              className={cn(
                "transition-all duration-700 ease-in-out",
                scrolled ? "ml-4" : "ml-8"
              )}
            >
              <Link
                to="/get-quote"
                className={cn(
                  "bg-gradient-to-r from-primary-500 to-primary-600 text-white font-semibold hover:shadow-lg hover:shadow-primary-500/25 transition-all duration-300 whitespace-nowrap",
                  scrolled ? "px-4 py-2 text-sm rounded-full" : "px-6 py-3 text-base rounded-lg"
                )}
              >
                Get a Quote
              </Link>
            </motion.div>
            </div>
          </motion.div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-black/95 backdrop-blur-xl border-t border-gray-700/50 shadow-2xl max-h-[calc(100vh-80px)] overflow-y-auto">
            <div className="px-6 py-4 space-y-3">
              <Link
                to="/services"
                className="block text-white hover:text-primary-400 py-2 transition-colors font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                Services
              </Link>
              <div className="space-y-1">
                <div className="text-gray-400 text-sm font-medium pt-2 pb-1">Solutions</div>
                <Link
                  to="/web-development"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Web Development
                </Link>
                <Link
                  to="/mobile-development"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Mobile Development
                </Link>
                <Link
                  to="/ui-ux-design"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  UI/UX Design
                </Link>
                <Link
                  to="/api-development"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  API Development
                </Link>
                <Link
                  to="/digital-experience-platforms"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Digital Platforms
                </Link>
                <Link
                  to="/performance-optimization"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Performance Optimization
                </Link>
                <Link
                  to="/experience-design"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Experience Design
                </Link>
              </div>
              <div className="space-y-1">
                <div className="text-gray-400 text-sm font-medium pt-2 pb-1">Industries</div>
                <Link
                  to="/ecommerce"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  E-commerce
                </Link>
                <Link
                  to="/saas"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  SaaS Platforms
                </Link>
                <Link
                  to="/education"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Education
                </Link>
                <Link
                  to="/healthcare"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Healthcare
                </Link>
                <Link
                  to="/fintech"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Fintech
                </Link>
                <Link
                  to="/banking"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Banking
                </Link>
              </div>
              <div className="space-y-1">
                <div className="text-gray-400 text-sm font-medium pt-2 pb-1">Company</div>
                <Link
                  to="/about"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  About Us
                </Link>
                <Link
                  to="/jobs"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Careers
                </Link>
                <Link
                  to="/blog"
                  className="block text-white hover:text-primary-400 py-1.5 pl-4 transition-colors text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Blog
                </Link>
              </div>
              <div className="pt-3 pb-2">
                <Link
                  to="/get-quote"
                  className="block bg-gradient-to-r from-primary-500 to-primary-600 text-white font-semibold py-2.5 px-6 rounded-lg text-center text-sm"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Get a Quote
                </Link>
              </div>
            </div>
          </div>
        )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ModernNavbar;
